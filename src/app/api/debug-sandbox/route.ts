import { debugSandbox, fixSandboxIssues } from "@/inngest/debug";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
    try {
        const { sandboxId, action } = await request.json();
        
        if (!sandboxId) {
            return NextResponse.json(
                { error: "sandboxId is required" },
                { status: 400 }
            );
        }
        
        if (action === "debug") {
            const debugInfo = await debugSandbox(sandboxId);
            return NextResponse.json({ 
                success: true, 
                debugInfo 
            });
        } else if (action === "fix") {
            const fixed = await fixSandboxIssues(sandboxId);
            return NextResponse.json({ 
                success: true, 
                fixed 
            });
        } else {
            return NextResponse.json(
                { error: "action must be 'debug' or 'fix'" },
                { status: 400 }
            );
        }
        
    } catch (error) {
        console.error("Debug sandbox error:", error);
        return NextResponse.json(
            { error: "Failed to debug sandbox", details: error instanceof Error ? error.message : String(error) },
            { status: 500 }
        );
    }
}
