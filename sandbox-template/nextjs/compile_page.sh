
#!/bin/bash

# This script runs during building the sandbox template
# and makes sure the Next.js app is (1) running and (2) the `/` page is compiled
function ping_server() {
	counter=0
	max_attempts=300  # 30 seconds max
	response=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost:3000")

	while [[ ${response} -ne 200 && ${counter} -lt ${max_attempts} ]]; do
	  let counter++
	  if  (( counter % 20 == 0 )); then
        echo "Waiting for server to start... (attempt ${counter}/${max_attempts})"
      fi
      sleep 0.1
	  response=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost:3000")
	done

	if [[ ${response} -eq 200 ]]; then
		echo "Server is responding successfully!"
	else
		echo "Server failed to start after ${max_attempts} attempts"
		exit 1
	fi
}

# Ensure we're in the right directory
cd /home/<USER>

# Kill any existing Next.js processes
pkill -f "next dev" || true
pkill -f "node.*next" || true

# Wait a moment for processes to terminate
sleep 2

echo "Starting Next.js development server..."

# Start the server in background and ping it
ping_server &
exec npx next dev --turbopack
